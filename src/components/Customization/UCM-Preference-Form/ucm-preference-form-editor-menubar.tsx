import { Editor } from '@tiptap/react';
import {
  Bold,
  Italic,
  Link as LinkIcon,
  List,
  ListOrdered,
  Palette,
  Redo,
  Type,
  UnderlineIcon,
  Undo,
  X as UnlinkIcon,
} from 'lucide-react';
import React, { ReactNode, useCallback, useEffect, useState } from 'react';
import { Button } from '../../../@/components/ui/Common/Elements/Button/Button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../../../@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../../../@/components/ui/tooltip';

interface MenuBarProperties {
  editor: Editor | null;
  onContentChange: (content: string) => void;
}

interface ButtonWrapperProperties {
  onClick: () => void;
  isActive: boolean;
  children: ReactNode;
  tooltip: string;
}

const ButtonWrapper: React.FC<ButtonWrapperProperties> = ({
  onClick,
  isActive,
  children,
  tooltip,
}) => (
  <TooltipProvider>
    <Tooltip>
      <TooltipTrigger>
        <Button
          type="button"
          onClick={onClick}
          className={`p-2 ${isActive ? 'bg-gray-200 dark:bg-gray-700' : ''}`}
          variant="ghost"
        >
          {children}
        </Button>
      </TooltipTrigger>
      <TooltipContent className="font-primary-text text-primary-background">
        <span>{tooltip}</span>
      </TooltipContent>
    </Tooltip>
  </TooltipProvider>
);

const font = [
  { name: 'Default', value: 'Arial, sans-serif' },
  { name: 'Serif', value: 'Georgia, serif' },
  { name: 'Monospace', value: 'Courier, monospace' },
];

const colors = [
  { name: 'Black', value: '#000000' },
  { name: 'Red', value: '#FF0000' },
  { name: 'Green', value: '#00FF00' },
  { name: 'Blue', value: '#0000FF' },
];

const UCMPreferenceFormEditorMenubar: React.FC<MenuBarProperties> = ({
  editor,
  onContentChange,
}) => {
  const [_, setEditorState] = useState(0);

  const handleUpdate = useCallback(() => {
    if (editor) {
      const content = editor.getHTML();
      onContentChange(content);
      setEditorState((prev) => prev + 1);
    }
  }, [editor, onContentChange]);

  useEffect(() => {
    if (editor) {
      editor.on('update', handleUpdate);

      return () => {
        editor.off('update', handleUpdate);
      };
    }
  }, [editor, handleUpdate]);

  const addLink = () => {
    const previousUrl = editor?.getAttributes('link').href;
    const url = prompt('Enter the URL', previousUrl || 'https://');

    // cancelled
    if (url === null) {
      return;
    }

    // empty
    if (url === '') {
      editor?.chain().focus().extendMarkRange('link').unsetLink().run();
      return;
    }

    // update link
    editor?.chain().focus().extendMarkRange('link').setLink({ href: url }).run();
  };

  const removeLink = () => {
    editor?.chain().focus().unsetLink().run();
  };

  if (!editor) {
    return null;
  }

  return (
    <div className="mb-4 flex items-center space-x-1 rounded p-1">
      <ButtonWrapper
        onClick={() => editor.chain().focus().toggleBold().run()}
        isActive={editor.isActive('bold')}
        tooltip="Bold"
      >
        <Bold className="size-5" />
      </ButtonWrapper>
      <ButtonWrapper
        onClick={() => editor.chain().focus().toggleItalic().run()}
        isActive={editor.isActive('italic')}
        tooltip="Italic"
      >
        <Italic className="size-5" />
      </ButtonWrapper>
      <ButtonWrapper
        onClick={() => editor.chain().focus().toggleUnderline().run()}
        isActive={editor.isActive('underline')}
        tooltip="Underline"
      >
        <UnderlineIcon className="size-5" />
      </ButtonWrapper>
      <ButtonWrapper
        onClick={() => editor.chain().focus().toggleBulletList().run()}
        isActive={editor.isActive('bulletList')}
        tooltip="Bullet List"
      >
        <List className="size-5" />
      </ButtonWrapper>
      <ButtonWrapper
        onClick={() => editor.chain().focus().toggleOrderedList().run()}
        isActive={editor.isActive('orderedList')}
        tooltip="Ordered List"
      >
        <ListOrdered className="size-5" />
      </ButtonWrapper>
      <ButtonWrapper
        onClick={() => editor.chain().focus().undo().run()}
        isActive={false}
        tooltip="Undo"
      >
        <Undo className="size-5" />
      </ButtonWrapper>
      <ButtonWrapper
        onClick={() => editor.chain().focus().redo().run()}
        isActive={false}
        tooltip="Redo"
      >
        <Redo className="size-5" />
      </ButtonWrapper>
      <ButtonWrapper onClick={addLink} isActive={editor.isActive('link')} tooltip="Add Link">
        <LinkIcon className="size-5" />
      </ButtonWrapper>
      <ButtonWrapper onClick={removeLink} isActive={false} tooltip="Remove Link">
        <UnlinkIcon className="size-5" />
      </ButtonWrapper>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <ButtonWrapper
            onClick={() => {}}
            isActive={false}
            tooltip="Font Style"
          >
            <Type className="size-5" />
          </ButtonWrapper>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          {font.map((fontItem) => (
            <DropdownMenuItem
              key={fontItem.name}
              onClick={() => editor.chain().focus().setFontFamily(fontItem.value).run()}
            >
              {fontItem.name}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <ButtonWrapper
            onClick={() => {}}
            isActive={false}
            tooltip="Text Color"
          >
            <Palette className="size-5" />
          </ButtonWrapper>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          {colors.map((color) => (
            <DropdownMenuItem
              key={color.name}
              onClick={() => editor.chain().focus().setColor(color.value).run()}
            >
              <div className="flex items-center">
                <div
                  className="mr-2 size-4 rounded-full"
                  style={{ backgroundColor: color.value }}
                />
                {color.name}
              </div>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export default UCMPreferenceFormEditorMenubar;
