import BulletList from '@tiptap/extension-bullet-list';
import ListItem from '@tiptap/extension-list-item';
import OrderedList from '@tiptap/extension-ordered-list';
import { Link } from '@tiptap/extension-link';
import Placeholder from '@tiptap/extension-placeholder';
import Underline from '@tiptap/extension-underline';
import { EditorContent, useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '../../../@/components/ui/Common/Elements/Accordian/Accordian';
import UploadFile from '../../../@/components/ui/Common/Elements/Dialog/UploadAlert';
import { Label } from '../../../@/components/ui/Common/Elements/Label/Label';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../@/components/ui/Common/Elements/Select/Select';
import { Input } from '../../../@/components/ui/Input';
import { ColorPicker } from '../../../@/components/ui/input-color';
import { Slider } from '../../../@/components/ui/slider';
import { Switch } from '../../../@/components/ui/switch';
import { Textarea } from '../../../@/components/ui/textarea';
import reseticon from '../../../assets/ResetIcon.svg';
import close from '../../../assets/close.svg';
import { FormConfiguration } from '../../../types/universal-consent-management';
import { uploadFile } from '../../common/services/cookie-consent-management';
import UCMPreferenceFormEditorMenubar from './ucm-preference-form-editor-menubar';

interface ConfigurationProperties {
  fontFamily: string;
  logoUrl: string;
  showLogo: boolean;
  showCookie: boolean;
  showConsent: boolean;
  showConsentFlow: boolean;
  showDSR: boolean;
  title: string;
  description: string;
  privacy_notice_heading: string;
  preference_center_heading: string;
  dsr_center_heading: string;
  consent_flow_heading: string;
  dsrURL: string;
  dpoEmail: string;
  dsrContent: string;
  consent_purpose_configuration: FormConfiguration;
}

interface UCMFormConfigurationProperties {
  config: ConfigurationProperties;
  defaultConfig: ConfigurationProperties;
  setConfig: React.Dispatch<React.SetStateAction<ConfigurationProperties>>;
}

const UCMPreferenceFormConfiguration: React.FC<UCMFormConfigurationProperties> = ({
  config,
  setConfig,
  defaultConfig,
}) => {
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        bulletList: false,
        orderedList: false,
      }),
      BulletList.configure({
        HTMLAttributes: {
          class: 'list-disc pl-6',
        },
      }),
      OrderedList.configure({
        HTMLAttributes: {
          class: 'list-decimal pl-6',
        },
      }),
      ListItem,
      Underline,
      Placeholder.configure({
        placeholder: 'DSR Content',
      }),
      Link.configure({
        openOnClick: false, // Configure as needed
      }),
    ],
    content: config?.dsrContent || '<p class="editor-placeholder">DSR Content...</p>',
  });

  //! State
  const [fileError, setFileError] = useState(false);

  //! Effects
  // Sync editor content when config.dsrContent changes (e.g., during reset)
  useEffect(() => {
    if (editor && config?.dsrContent !== undefined) {
      const currentContent = editor.getHTML();
      if (currentContent !== config.dsrContent) {
        editor.commands.setContent(config.dsrContent);
      }
    }
  }, [editor, config?.dsrContent]);

  //! Logs

  //! Handlers
  const handleEditorUpdate = useCallback(
    (content: string) => {
      updateConfig('dsrContent', content);
    },
    [config?.dsrContent]
  );

  const memoizedMenuBar = useMemo(
    () => <UCMPreferenceFormEditorMenubar editor={editor} onContentChange={handleEditorUpdate} />,
    [editor, handleEditorUpdate]
  );

  const updateConfig = (key: string, value: any) => {
    setConfig((prev) => ({ ...prev, [key]: value }));
  };

  const updateFormConfigValue = (path: string, value: any) => {
    setConfig((prev) => {
      const newConfig = structuredClone(prev);
      const keys = path.split('.');
      let current: any = newConfig;

      for (let i = 0; i < keys?.length - 1; i++) {
        current = current[keys[i]];
      }

      current[keys[keys?.length - 1]] = value;
      return newConfig;
    });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const validTypes = ['image/jpeg', 'image/jpg', 'image/png'];

    if (!validTypes.includes(file.type)) {
      setFileError(true);
      updateConfig('logoUrl', '');
      return;
    }

    setFileError(false);
    handleFileUpload(file);
  };

  const handleFileUpload = async (file: File) => {
    try {
      const formData = new FormData();
      formData.append('files', file);

      const response = await uploadFile(formData);
      const uploadedLogoUrl = response?.result?.url;

      if (uploadedLogoUrl) updateConfig('logoUrl', uploadedLogoUrl);
    } catch (error) {
      console.error('File upload failed:', error);
    }
  };

  const resetConfig = () => {
    setConfig({
      fontFamily: defaultConfig.fontFamily,
      logoUrl: defaultConfig.logoUrl,
      showLogo: true,
      showCookie: defaultConfig.showCookie,
      showConsent: defaultConfig.showConsent,
      showDSR: defaultConfig.showDSR,
      showConsentFlow: defaultConfig.showConsentFlow,
      title: defaultConfig.title,
      description: defaultConfig.description,
      privacy_notice_heading: defaultConfig.privacy_notice_heading,
      preference_center_heading: defaultConfig.preference_center_heading,
      dsr_center_heading: defaultConfig.dsr_center_heading,
      consent_flow_heading: defaultConfig.consent_flow_heading,
      dsrURL: defaultConfig.dsrURL,
      dpoEmail: defaultConfig.dpoEmail,
      dsrContent: defaultConfig.dsrContent,
      consent_purpose_configuration: defaultConfig.consent_purpose_configuration,
    });
  };

  const configurations = [
    {
      label: 'Customize Preference Form',
      id: 1,
      content: (
        <section className="flex h-fit flex-col gap-2 bg-secondary p-4 font-primary-text">
          {/* {activePicker === 'r1-input' && (
            <div ref={pickerRef} className="relative">
              <button
                className="bg-white-300 hover:bg-white-400 text-black/2 absolute right-2 top-0 rounded-full font-bold"
                onClick={() => setActivePicker('')}
              >
                &times;
              </button>
              <div className="bg-white p-4">
                <SketchPicker
                  color={config?.backgroundHeaderColor}
                  onChangeComplete={(color) => {
                    updateConfig('backgroundHeaderColor', color.hex);
                    setActivePicker('');
                  }}
                  className="boder-0 mt-1 w-[100px] p-8" // Adds margin for spacing
                  width="92%"
                />
              </div>
            </div>
          )} */}
          <p className="text-base font-normal">Logo</p>
          <div className="ml-2 flex w-auto flex-wrap justify-between overflow-hidden rounded-lg border border-[#C0CDE0] bg-[#FFFFFF] p-3 text-sm">
            <UploadFile
              supportedFiles=".png, .jpg, .jpeg"
              errorMessage="Only .jpg , .png and .jpeg files are supported"
              isError={fileError}
              handleFileChange={handleFileChange}
            />
            {config.logoUrl && (
              <div className="flex items-center gap-4">
                <img src={config.logoUrl} alt="Uploaded Logo" className="max-w-8" />
                <button onClick={() => updateConfig('logoUrl', '')}>
                  <img src={close} alt="Remove Logo" />
                </button>
              </div>
            )}
          </div>
          {/* Logo Configuration */}
          <div className="flex h-fit w-full flex-col gap-2">
            <div className="flex h-fit w-full flex-col gap-2">
              <p>Logo Width</p>
              <Slider
                max={300}
                step={1}
                className="w-full rounded-md border border-primary-border"
                value={[
                  Number(config?.consent_purpose_configuration?.form?.logo?.width?.split('px')[0]),
                ]}
                onValueChange={(value) => {
                  updateFormConfigValue(
                    'consent_purpose_configuration.form.logo.width',
                    `${value[0]}px`
                  );
                }}
              />
            </div>
            {/* Logo Height */}
            <div className="flex h-fit w-full flex-col gap-2">
              <p>Logo Height</p>
              <Slider
                max={300}
                step={1}
                className="w-full rounded-md border border-primary-border"
                value={[
                  Number(config?.consent_purpose_configuration?.form?.logo?.height?.split('px')[0]),
                ]}
                onValueChange={(value) => {
                  updateFormConfigValue(
                    'consent_purpose_configuration.form.logo.height',
                    `${value[0]}px`
                  );
                }}
              />
            </div>
          </div>
          {/* Display Logo Switch */}
          <div className="flex items-center gap-2">
            <Switch
              id="toggleLogo"
              checked={config.showLogo}
              onCheckedChange={(value) => updateConfig('showLogo', value)}
            />
            <p>Display Logo on banner</p>
          </div>
          {/* Cookie Consent Toggle */}
          <div className="flex items-center gap-2">
            <Switch
              id="cookieToggle"
              checked={config.showCookie}
              onCheckedChange={(value) => updateConfig('showCookie', value)}
            />
            <p>Enable Cookie Consent</p>
          </div>
          {/* Universal Consent Toggle */}
          <div className="flex items-center gap-2">
            <Switch
              id="consentToggle"
              checked={config.showConsent}
              onCheckedChange={(value) => updateConfig('showConsent', value)}
            />
            <p>Enable Consent</p>
          </div>
          {/* DSR Toggle */}
          <div className="flex items-center gap-2">
            <Switch
              id="dsrToggle"
              checked={config.showDSR}
              onCheckedChange={(value) => updateConfig('showDSR', value)}
            />
            <p>Enable DSR</p>
          </div>
          {/* Consent Flow Toggle */}
          <div className="flex items-center gap-2">
            <Switch
              id="consentFlowToggle"
              checked={config.showConsentFlow}
              onCheckedChange={(value) => updateConfig('showConsentFlow', value)}
            />
            <p>Enable Consent Flow</p>
          </div>
          {/* Text Size */}
          {/* <Label>Text Size</Label>
          <div className="flex gap-2">
            {['small', 'medium', 'large'].map((size) => (
              <button
                key={size}
                className={`w-1/3 rounded-lg border bg-white p-2 text-center ${
                  config.textSize === size ? 'border-[#192440]' : ''
                }`}
                onClick={() => updateConfig('textSize', size)}
              >
                <span
                  style={{
                    fontSize: size === 'small' ? '12px' : size === 'large' ? '18px' : '14px',
                  }}
                >
                  A
                </span>
                <p>{size.charAt(0).toUpperCase() + size.slice(1)}</p>
              </button>
            ))}
          </div> */}
          {/* Form Heading */}
          {/* <Label>Heading</Label>
          <input
            type="text"
            value={config.formHeading}
            onChange={(e) => updateConfig('formHeading', e.target.value)}
            className="w-full rounded-md border p-2"
          /> */}
          {/* Form Heading */}
          {/* <Label>Section 1 Heading</Label>
          <input
            type="text"
            value={config.sectionOneHeading}
            onChange={(e) => updateConfig('sectionOneHeading', e.target.value)}
            className="w-full rounded-md border p-2"
          /> */}
          {/* Form Heading */}
          {/* <Label>Section 2 Heading</Label>
          <input
            type="text"
            value={config.sectionTwoHeading}
            onChange={(e) => updateConfig('sectionTwoHeading', e.target.value)}
            className="w-full rounded-md border p-2"
          /> */}
          {/* Font Family */}
          <Label>Font Family</Label>
          <Select
            value={config.fontFamily}
            onValueChange={(value) => updateConfig('fontFamily', value)}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                {['Poppins', 'Arial', 'Helvetica', 'Times New Roman'].map((font) => (
                  <SelectItem key={font} value={font}>
                    {font}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>

          <Label>Title</Label>
          <div className="flex items-center gap-2 rounded-md border bg-white p-2">
            <input
              value={config.title}
              className="w-full bg-transparent outline-none"
              onChange={(event) => updateConfig('title', event?.target?.value)}
            />
          </div>
          <Label>Description</Label>
          <div className="flex items-center gap-2 rounded-md border bg-white p-2">
            <textarea
              value={config.description}
              className="w-full bg-transparent outline-none"
              rows={10}
              onChange={(event) => updateConfig('description', event?.target?.value)}
            />
          </div>
          <Label>Privacy Notice Tab Heading</Label>
          <div className="flex items-center gap-2 rounded-md border bg-white p-2">
            <input
              value={config.privacy_notice_heading}
              className="w-full bg-transparent outline-none"
              onChange={(event) => updateConfig('privacy_notice_heading', event?.target?.value)}
            />
          </div>
          <Label>Consent Preferences Tab Heading</Label>
          <div className="flex items-center gap-2 rounded-md border bg-white p-2">
            <input
              value={config.preference_center_heading}
              className="w-full bg-transparent outline-none"
              onChange={(event) => updateConfig('preference_center_heading', event?.target?.value)}
            />
          </div>
          <Label>DSR Tab Heading</Label>
          <div className="flex items-center gap-2 rounded-md border bg-white p-2">
            <input
              value={config.dsr_center_heading}
              className="w-full bg-transparent outline-none"
              onChange={(event) => updateConfig('dsr_center_heading', event?.target?.value)}
            />
          </div>
          <Label>Consent Flow Tab Heading</Label>
          <div className="flex items-center gap-2 rounded-md border bg-white p-2">
            <input
              value={config.consent_flow_heading}
              className="w-full bg-transparent outline-none"
              onChange={(event) => updateConfig('consent_flow_heading', event?.target?.value)}
            />
          </div>
          <Label>DSR URL</Label>
          <div className="flex items-center gap-2 rounded-md border bg-white p-2">
            <input
              value={config.dsrURL}
              className="w-full bg-transparent outline-none"
              onChange={(event) => updateConfig('dsrURL', event?.target?.value)}
            />
          </div>
          <Label>DPO Email</Label>
          <div className="flex items-center gap-2 rounded-md border bg-white p-2">
            <input
              value={config.dpoEmail}
              className="w-full bg-transparent outline-none"
              onChange={(event) => updateConfig('dpoEmail', event?.target?.value)}
            />
          </div>
          {/* Background Color */}
          {/* <Label>Color Scheme</Label>
          <div className="flex items-center gap-2 rounded-md border bg-white p-2">
            <input
              type="text"
              value={config.backgroundHeaderColor}
              readOnly
              className="w-full bg-transparent"
            />
            <button
              className="h-6 w-6 cursor-pointer rounded-full border"
              style={{ backgroundColor: config.backgroundHeaderColor }}
              onClick={() => setActivePicker('backgroundHeaderColor')}
            />
          </div>
          {activePicker === 'backgroundHeaderColor' && (
            <SketchPicker
              color={config.backgroundHeaderColor}
              onChangeComplete={(color) => {
                updateConfig('backgroundHeaderColor', color.hex);
                setActivePicker('');
              }}
            />
          )} */}
          {/* Font Color */}
          {/* <Label>Font Color</Label>
          <div className="flex items-center gap-2 rounded-md border bg-white p-2">
            <input
              type="text"
              value={config.fontColor}
              readOnly
              className="w-full bg-transparent"
            />
            <button
              className="h-6 w-6 cursor-pointer rounded-full border"
              style={{ backgroundColor: config.fontColor }}
              onClick={() => setActivePicker('fontColor')}
            />
          </div> */}
          {/* Footer Content */}
          <Label>DSR Content </Label>
          <div className="flex h-fit min-h-[200px] flex-col gap-2 rounded-md border bg-white p-2">
            {/* <textarea
              value={config.dsrContent}
              className="w-full bg-transparent outline-none"
              rows={10}
              onChange={(event) => updateConfig('dsrContent', event?.target?.value)}
            /> */}
            {memoizedMenuBar}
            <EditorContent editor={editor} className="h-full w-full bg-gray-50 p-2" />
          </div>

          {/* {activePicker === 'fontColor' && (
            <SketchPicker
              color={config.fontColor}
              onChangeComplete={(color) => {
                updateConfig('fontColor', color.hex);
                setActivePicker('');
              }}
            />
          )} */}

          {/* Reset Button */}
          <button onClick={resetConfig} className="flex items-center gap-2 text-blue-500">
            <img src={reseticon} alt="Reset Icon" />
            Reset Changes
          </button>
        </section>
      ),
    },
    {
      label: 'Customize Consent Preferences',
      id: 2,
      content: (
        <section className="flex h-fit w-full flex-col gap-2 bg-secondary p-4 font-primary-text">
          {/* Form Configuration */}
          <div className="flex h-fit w-full flex-col gap-4">
            <p className="text-lg font-bold">Consent Configuration</p>

            {/* Color Scheme */}
            <div className="flex h-fit w-full flex-col gap-2 pl-2">
              <ColorPicker
                color={config?.consent_purpose_configuration?.form?.color_scheme}
                onChange={(value) =>
                  updateFormConfigValue('consent_purpose_configuration.form.color_scheme', value)
                }
                label="Form Color Scheme"
              />
            </div>

            {/* Border Radius */}
            <div className="flex h-fit w-full flex-col gap-2 pl-2">
              <Label>Border Radius</Label>
              <Input
                type="text"
                value={config?.consent_purpose_configuration?.form?.border_radius}
                onChange={(e) =>
                  updateFormConfigValue(
                    'consent_purpose_configuration.form.border_radius',
                    e.target.value
                  )
                }
                className="w-full rounded-md border p-2"
              />
            </div>
          </div>

          {/* PII Configuration */}
          <div className="flex h-fit w-full flex-col gap-4">
            {/* Show Badge */}
            <div className="flex h-fit w-full gap-2 pl-2">
              <Switch
                id="toggleLogo"
                checked={config?.consent_purpose_configuration?.pii_section?.show_badges}
                onCheckedChange={(value) =>
                  updateFormConfigValue(
                    'consent_purpose_configuration.pii_section.show_badges',
                    value
                  )
                }
              />
              <p>Show Badges</p>
            </div>
          </div>

          {/* Consent Configuration */}
          <div className="flex h-fit w-full flex-col gap-4">
            {/* Heading Configuration */}
            <div className="flex h-fit w-full flex-col gap-2 pl-2">
              {/* Consent Heading */}
              <div className="flex h-fit w-full flex-col gap-2">
                <p>Consent Section Heading</p>
                <Input
                  type="text"
                  value={
                    config?.consent_purpose_configuration?.consent_collection_section?.heading.text
                  }
                  onChange={(e) =>
                    updateFormConfigValue(
                      'consent_purpose_configuration.consent_collection_section.heading.text',
                      e.target.value
                    )
                  }
                  className="w-full rounded-md border p-2"
                />
              </div>
              {/* Consent Heading Size*/}
              <div className="flex h-fit w-full flex-col gap-2">
                <p>Consent Section Heading Size</p>
                <Input
                  type="text"
                  value={
                    config?.consent_purpose_configuration?.consent_collection_section?.heading?.size
                  }
                  onChange={(e) =>
                    updateFormConfigValue(
                      'consent_purpose_configuration.consent_collection_section.heading.size',
                      e.target.value
                    )
                  }
                  className="w-full rounded-md border p-2"
                />
              </div>
              {/* Consent Section Heading Color */}
              <div className="flex h-fit w-full flex-col gap-2">
                <ColorPicker
                  color={
                    config?.consent_purpose_configuration?.consent_collection_section?.heading
                      ?.color
                  }
                  onChange={(value) =>
                    updateFormConfigValue(
                      'consent_purpose_configuration.consent_collection_section.heading.color',
                      value
                    )
                  }
                  label="Consent Section Heading Color"
                />
              </div>
            </div>

            {/* Description Configuration */}
            <div className="flex h-fit w-full flex-col gap-2 pl-2">
              {/* Consent Section description */}
              <div className="flex h-fit w-full flex-col gap-2">
                <p>Consent Section Description</p>
                <Input
                  type="text"
                  value={
                    config?.consent_purpose_configuration?.consent_collection_section?.description
                      ?.text
                  }
                  onChange={(e) =>
                    updateFormConfigValue(
                      'consent_purpose_configuration.consent_collection_section.description.text',
                      e.target.value
                    )
                  }
                  className="w-full rounded-md border p-2"
                />
              </div>
              {/* Consent Section description Size*/}
              <div className="flex h-fit w-full flex-col gap-2">
                <p>Consent Section Description Size</p>
                <Input
                  type="text"
                  value={
                    config?.consent_purpose_configuration?.consent_collection_section?.description
                      ?.size
                  }
                  onChange={(e) =>
                    updateFormConfigValue(
                      'consent_purpose_configuration.consent_collection_section.description.size',
                      e.target.value
                    )
                  }
                  className="w-full rounded-md border p-2"
                />
              </div>
              {/* Consent Section description Color */}
              <div className="flex h-fit w-full flex-col gap-2">
                <ColorPicker
                  color={
                    config?.consent_purpose_configuration?.consent_collection_section?.description
                      ?.color
                  }
                  onChange={(value) =>
                    updateFormConfigValue(
                      'consent_purpose_configuration.consent_collection_section.description.color',
                      value
                    )
                  }
                  label="Form Description Color"
                />
              </div>
            </div>
            {/* Consent Section description */}
            <div className="flex h-fit w-full flex-col gap-2 pl-2">
              <p>All Checkbox Text</p>
              <Input
                type="text"
                value={
                  config?.consent_purpose_configuration?.consent_collection_section
                    ?.all_checkbox_text
                }
                onChange={(e) =>
                  updateFormConfigValue(
                    'consent_purpose_configuration.consent_collection_section.all_checkbox_text',
                    e.target.value
                  )
                }
                className="w-full rounded-md border p-2"
              />
            </div>
            {/* Show CheckAll*/}
            <div className="flex h-fit w-full gap-2 pl-2">
              <Switch
                id="toggleLogo"
                checked={
                  config?.consent_purpose_configuration?.consent_collection_section
                    ?.show_check_all_checkbox
                }
                onCheckedChange={(value) =>
                  updateFormConfigValue(
                    'consent_purpose_configuration.consent_collection_section.show_check_all_checkbox',
                    value
                  )
                }
              />
              <p>Enable Bulk Select</p>
            </div>
          </div>

          {/* Footer Configuration */}
          <div className="flex h-fit w-full flex-col gap-4">
            <p className="text-lg font-bold">Form Footer Configuration</p>

            {/* Heading Configuration */}
            <div className="flex h-fit w-full flex-col gap-2 pl-2">
              {/* Footer Heading */}
              <div className="flex h-fit w-full flex-col gap-2">
                <p>Footer Heading</p>
                <Input
                  type="text"
                  value={config?.consent_purpose_configuration?.form_footer?.heading?.text}
                  onChange={(e) =>
                    updateFormConfigValue(
                      'consent_purpose_configuration.form_footer.heading.text',
                      e.target.value
                    )
                  }
                  className="w-full rounded-md border p-2"
                />
              </div>
              {/* Footer Heading Size*/}
              <div className="flex h-fit w-full flex-col gap-2">
                <p>Footer Heading Size</p>
                <Input
                  type="text"
                  value={config?.consent_purpose_configuration?.form_footer?.heading?.size}
                  onChange={(e) =>
                    updateFormConfigValue(
                      'consent_purpose_configuration.form_footer.heading.size',
                      e.target.value
                    )
                  }
                  className="w-full rounded-md border p-2"
                />
              </div>
              {/* Footer Heading Color */}
              <div className="flex h-fit w-full flex-col gap-2">
                <ColorPicker
                  color={config?.consent_purpose_configuration?.form_footer?.heading?.color}
                  onChange={(value) =>
                    updateFormConfigValue(
                      'consent_purpose_configuration.form_footer.heading.color',
                      value
                    )
                  }
                  label="Footer Heading Color"
                />
              </div>
            </div>

            {/* Description Configuration */}
            <div className="flex h-fit w-full flex-col gap-2 pl-2">
              {/* Footer description */}
              <div className="flex h-fit w-full flex-col gap-2">
                <p>Footer Description</p>
                <Textarea
                  rows={5}
                  value={config?.consent_purpose_configuration?.form_footer?.description?.text}
                  onChange={(e) =>
                    updateFormConfigValue(
                      'consent_purpose_configuration.form_footer.description.text',
                      e.target.value
                    )
                  }
                  className="w-full rounded-md border p-2"
                />
              </div>
              {/* Footer description Size*/}
              <div className="flex h-fit w-full flex-col gap-2">
                <p>Footer Description Size</p>
                <Input
                  type="text"
                  value={config?.consent_purpose_configuration?.form_footer?.description?.size}
                  onChange={(e) =>
                    updateFormConfigValue(
                      'consent_purpose_configuration.form_footer.description.size',
                      e.target.value
                    )
                  }
                  className="w-full rounded-md border p-2"
                />
              </div>
              {/* Footer description Color */}
              <div className="flex h-fit w-full flex-col gap-2">
                <ColorPicker
                  color={config?.consent_purpose_configuration?.form_footer?.description?.color}
                  onChange={(value) =>
                    updateFormConfigValue(
                      'consent_purpose_configuration.form_footer.description.color',
                      value
                    )
                  }
                  label="Footer Description Color"
                />
              </div>
            </div>
          </div>

          {/* Submit Button Configuration */}
          <div className="flex h-fit w-full flex-col gap-4">
            <p className="text-lg font-bold">Submit Button Configuration</p>

            {/* Heading Configuration */}
            <div className="flex h-fit w-full flex-col gap-2 pl-2">
              {/* Button Heading */}
              <div className="flex h-fit w-full flex-col gap-2">
                <p>Button Heading</p>
                <Input
                  type="text"
                  value={config?.consent_purpose_configuration?.submit_button?.text}
                  onChange={(e) =>
                    updateFormConfigValue(
                      'consent_purpose_configuration.submit_button.text',
                      e.target.value
                    )
                  }
                  className="w-full rounded-md border p-2"
                />
              </div>
              {/* Button Heading Size*/}
              <div className="flex h-fit w-full flex-col gap-2">
                <p>Button Heading Size</p>
                <Input
                  type="text"
                  value={config?.consent_purpose_configuration?.submit_button?.size}
                  onChange={(e) =>
                    updateFormConfigValue(
                      'consent_purpose_configuration.submit_button.size',
                      e.target.value
                    )
                  }
                  className="w-full rounded-md border p-2"
                />
              </div>
              {/* Button Heading Color */}
              <div className="flex h-fit w-full flex-col gap-2">
                <ColorPicker
                  color={config?.consent_purpose_configuration?.submit_button?.color}
                  onChange={(value) =>
                    updateFormConfigValue(
                      'consent_purpose_configuration.submit_button.color',
                      value
                    )
                  }
                  label="Button Color"
                />
              </div>
            </div>
          </div>

          <div className="flex h-fit w-full flex-col gap-4">
            <p className="text-lg font-bold">Reset Configuration</p>
            <div className="flex h-fit w-full flex-col gap-2 pl-2">
              <button onClick={resetConfig} className="rounded-md border bg-red-500 p-2 text-white">
                Reset Configuration
              </button>
            </div>
          </div>
        </section>
      ),
    },
  ];

  return (
    <div className={`h-auto max-h-full w-full overflow-auto rounded-lg bg-white`}>
      <Accordion
        type="multiple"
        className="w-full"
        defaultValue={configurations.length > 0 ? [configurations[0]?.id?.toString()] : []}
      >
        {configurations?.map((item) => (
          <>
            <AccordionItem value={item?.id?.toString()} className="border-0 px-3">
              <AccordionTrigger className="border-0 text-lg font-medium text-secondary-text hover:no-underline">
                {item?.label}
              </AccordionTrigger>
              <AccordionContent className="flex h-fit w-full flex-col gap-6 px-1 pb-5">
                {item?.content}
              </AccordionContent>
            </AccordionItem>
            <hr className="h-0.5 bg-primary-border" />
          </>
        ))}
      </Accordion>
    </div>
  );
};

export default UCMPreferenceFormConfiguration;
